'use client';

import { useState, useEffect, useMemo, memo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';

type MenuPermission = {
  menuId: string;
  name: string;
  displayName: string;
  path: string;
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  order?: number;
};

function DynamicSidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();
  // For tenant_sale users, we'll use the menu permissions from the session
  // This is a fallback in case the session doesn't have menu permissions yet
  const [userMenus, setUserMenus] = useState<MenuPermission[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // If session has menu permissions, use them
    if (session?.user?.role === 'tenant_sale' && session?.user?.menuPermissions) {
      console.log('Using menu permissions from session in sidebar');

      // Filter out the dashboard menu item to avoid duplication
      // Also filter to only include menus with canView permission
      const filteredMenus = (session.user.menuPermissions as MenuPermission[])
        .filter(menu =>
          menu.name !== 'dashboard' &&
          menu.path !== '/dashboard' &&
          menu.canView === true
        );

      // Sort menus by order
      const sortedMenus = [...filteredMenus].sort((a, b) =>
        (a.order || 999) - (b.order || 999)
      );

      setUserMenus(sortedMenus);
      setIsLoading(false);

      console.log(`Loaded ${sortedMenus.length} menus for sidebar`);
    }
    // If session doesn't have menu permissions yet, fetch them (fallback for older sessions)
    else if (session?.user?.role === 'tenant_sale' && !session?.user?.menuPermissions) {
      console.log('No menu permissions in session, fetching from API');
      fetchUserMenus();
    } else {
      setIsLoading(false);
    }
  }, [session]);

  const fetchUserMenus = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching menus from API...');
      const response = await fetch('/api/user/menus');
      const data = await response.json();

      if (data.success) {
        console.log(`API returned ${data.menus.length} menus`);

        // Filter out the dashboard menu item to avoid duplication
        // Also filter to only include menus with canView permission
        const filteredMenus = data.menus.filter(
          (menu: MenuPermission) =>
            menu.name !== 'dashboard' &&
            menu.path !== '/dashboard' &&
            menu.canView === true
        );

        // Sort menus by order
        const sortedMenus = [...filteredMenus].sort((a, b) =>
          (a.order || 999) - (b.order || 999)
        );

        console.log(`Setting ${sortedMenus.length} menus for sidebar`);
        setUserMenus(sortedMenus);
      } else {
        console.error('Failed to fetch user menus:', data.error);
      }
    } catch (error) {
      console.error('Error fetching user menus:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isActive = (path: string) => {
    // Exact match
    if (pathname === path) {
      return true;
    }

    // Special case for categories - don't highlight products when on categories page
    if (path === '/dashboard/products' && pathname === '/dashboard/products/categories') {
      return false;
    }

    // For other cases, check if pathname starts with the path
    return pathname?.startsWith(`${path}/`);
  };

  // Render admin menu items
  const renderAdminMenuItems = useMemo(() => (
    <>
      <li>
        <Link
          href="/dashboard/users"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/users') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Users
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/tenants"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/tenants') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Tenants
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/vendors"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/vendors') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Vendors
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/settings"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/settings') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Settings
        </Link>
      </li>
    </>
  ), [pathname]);

  // Render tenant menu items
  const renderTenantMenuItems = useMemo(() => (
    <>
      <li>
        <Link
          href="/dashboard/sales"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/sales') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Invoice
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/refunds"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/refunds') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Refunds
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/expenses"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/expenses') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Expenses
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/customers"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/customers') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Customers
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/dealers"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/dealers') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Dealers
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/products"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/products') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Products
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/inventory"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/inventory') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Inventory
        </Link>
      </li>
      {/* Branch Transfer menu item commented out
      <li>
        <Link
          href="/dashboard/inventory/branch-transfer"
          className={`block pl-8 py-1 text-sm rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/inventory/branch-transfer') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Branch Transfer
        </Link>
      </li>
      */}
      <li>
        <Link
          href="/dashboard/branches"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/branches') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Branches
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/products/categories"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/products/categories') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Category
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/vendors"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/vendors') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Vendors
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/payments"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/payments') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Payments
        </Link>
      </li>
      {/* Brand Settings menu item removed as requested */}
      <li>
        <Link
          href="/dashboard/tenant/users"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/tenant/users') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Sales Users
        </Link>
      </li>
      <li>
        <Link
          href="/dashboard/reports"
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive('/dashboard/reports') ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          Reports
        </Link>
      </li>
    </>
  ), [pathname]);

  // Render tenant_sale menu items based on permissions
  const renderTenantSaleMenuItems = useMemo(() => {
    if (isLoading) {
      return <li className="px-4 py-2 text-gray-500">Loading menus...</li>;
    }

    if (userMenus.length === 0) {
      return <li className="px-4 py-2 text-gray-500">No menu access</li>;
    }

    return userMenus.map((menu) => (
      <li key={menu.menuId}>
        <Link
          href={menu.path}
          className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${isActive(menu.path) ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
        >
          {menu.displayName}
        </Link>
      </li>
    ));
  }, [isLoading, userMenus, pathname]);

  return (
    <nav className="p-4">
      <ul className="space-y-2">
        <li>
          <Link
            href="/dashboard"
            className={`block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${pathname === '/dashboard' ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400' : ''}`}
          >
            Dashboard
          </Link>
        </li>

        {session?.user?.role === 'admin' && renderAdminMenuItems}
        {session?.user?.role === 'tenant' && renderTenantMenuItems}
        {session?.user?.role === 'tenant_sale' && renderTenantSaleMenuItems}
      </ul>
    </nav>
  );
}

// Export as memoized component to prevent unnecessary re-renders
export default memo(DynamicSidebar);
