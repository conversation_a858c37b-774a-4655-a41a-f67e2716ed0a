import { auth } from "../../../../auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { paymentMethods } from "@/db/schema";
import { eq } from "drizzle-orm";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2 } from "lucide-react";
import PaymentMethodActions from "./_components/payment-method-actions";

export default async function PaymentMethodsPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/");
  }

  // Only tenant and admin users can access payment methods
  if (session.user.role !== 'tenant' && session.user.role !== 'admin') {
    redirect("/dashboard");
  }

  // Get tenant ID based on user role
  let tenantId = session.user.id;
  if (session.user.role === 'admin') {
    // For admin, we might want to show all or allow selection
    // For now, we'll show their own
    tenantId = session.user.id;
  }

  // Fetch payment methods for the tenant
  const methods = await db.query.paymentMethods.findMany({
    where: eq(paymentMethods.tenantId, tenantId),
    orderBy: (paymentMethods, { asc }) => [asc(paymentMethods.name)],
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Payment Methods</h1>
          <p className="text-muted-foreground">
            Manage your payment methods for sales and transactions
          </p>
        </div>
        <Link href="/dashboard/payment-methods/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Payment Method
          </Button>
        </Link>
      </div>

      <div className="grid gap-4">
        {methods.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">No Payment Methods</h3>
                <p className="text-muted-foreground mb-4">
                  You haven't created any payment methods yet. Create your first payment method to get started.
                </p>
                <Link href="/dashboard/payment-methods/new">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment Method
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {methods.map((method) => (
              <Card key={method.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{method.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      {method.isDefault && (
                        <Badge variant="default">Default</Badge>
                      )}
                      <Badge variant={method.isActive ? "secondary" : "destructive"}>
                        {method.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription>
                    Code: {method.code}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {method.description && (
                    <p className="text-sm text-muted-foreground mb-4">
                      {method.description}
                    </p>
                  )}
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      Created: {new Date(method.createdAt).toLocaleDateString()}
                    </div>
                    <PaymentMethodActions method={method} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
