// Static list of all available menus in the system
export const ALL_MENUS = [
  {
    id: 'dashboard',
    name: 'dashboard',
    displayName: 'Dashboard',
    description: 'Main dashboard',
    path: '/dashboard',
    icon: 'dashboard',
    order: 1,
    isActive: true,
    requiresPermission: false // Dashboard doesn't require permission check
  },
  {
    id: 'sales',
    name: 'sales',
    displayName: 'Invoice',
    description: 'Manage sales',
    path: '/dashboard/sales',
    icon: 'sales',
    order: 2,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'refunds',
    name: 'refunds',
    displayName: 'Refunds',
    description: 'Manage product refunds and damaged items',
    path: '/dashboard/refunds',
    icon: 'refunds',
    order: 3,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'expenses',
    name: 'expenses',
    displayName: 'Expenses',
    description: 'Manage expenses',
    path: '/dashboard/expenses',
    icon: 'expenses',
    order: 4,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'customers',
    name: 'customers',
    displayName: 'Customers',
    description: 'Manage customers',
    path: '/dashboard/customers',
    icon: 'customers',
    order: 5,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'dealers',
    name: 'dealers',
    displayName: 'Dealers',
    description: 'Manage dealers and suppliers',
    path: '/dashboard/dealers',
    icon: 'dealers',
    order: 6,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'products',
    name: 'products',
    displayName: 'Products',
    description: 'Manage products',
    path: '/dashboard/products',
    icon: 'products',
    order: 7,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'inventory',
    name: 'inventory',
    displayName: 'Inventory',
    description: 'Manage inventory',
    path: '/dashboard/inventory',
    icon: 'inventory',
    order: 8,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'branches',
    name: 'branches',
    displayName: 'Branches',
    description: 'Manage branches',
    path: '/dashboard/branches',
    icon: 'branches',
    order: 9,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'categories',
    name: 'categories',
    displayName: 'Categories',
    description: 'Manage product categories',
    path: '/dashboard/products/categories',
    icon: 'categories',
    order: 10,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'warehouse',
    name: 'warehouse',
    displayName: 'Warehouse',
    description: 'Manage warehouse inventory',
    path: '/dashboard/warehouse',
    icon: 'warehouse',
    order: 11,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'payments',
    name: 'payments',
    displayName: 'Payments',
    description: 'Manage payments',
    path: '/dashboard/payments',
    icon: 'payments',
    order: 12,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'users',
    name: 'users',
    displayName: 'Users',
    description: 'Manage users',
    path: '/dashboard/users',
    icon: 'users',
    order: 14,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'reports',
    name: 'reports',
    displayName: 'Reports',
    description: 'View reports',
    path: '/dashboard/reports',
    icon: 'reports',
    order: 15,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'sales_new',
    name: 'sales_new',
    displayName: 'New Sale',
    description: 'Create new sales',
    path: '/dashboard/sales/new',
    icon: 'sales_new',
    order: 16,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'expense_categories',
    name: 'expense_categories',
    displayName: 'Expense Categories',
    description: 'Manage expense categories',
    path: '/dashboard/expenses/categories',
    icon: 'expense_categories',
    order: 17,
    isActive: true,
    requiresPermission: true
  },
  {
    id: 'order_reports',
    name: 'order_reports',
    displayName: 'Order Reports',
    description: 'View order reports',
    path: '/dashboard/reports/orders',
    icon: 'order_reports',
    order: 18,
    isActive: true,
    requiresPermission: true
  },
];

// Helper function to get menu by path
export function getMenuByPath(path: string) {
  return ALL_MENUS.find(menu => menu.path === path);
}

// Helper function to check if a path requires permission
export function pathRequiresPermission(path: string): boolean {
  const menu = getMenuByPath(path);
  // If menu is not found, no permission required
  if (!menu) {
    return false;
  }
  // Use the requiresPermission property from the menu definition
  return menu.requiresPermission;
}

// Helper function to get all menus with permissions based on user's assigned permissions
export function getAllMenusWithPermissions(userId: string, tenantId?: string, userPermissions?: any[]) {
  // Create a map of menu permissions by menuId
  const permissionsMap = new Map();

  // If user permissions are provided, add them to the map
  if (userPermissions && userPermissions.length > 0) {
    userPermissions.forEach(permission => {
      permissionsMap.set(permission.menuId, {
        canView: permission.canView,
        canCreate: permission.canCreate,
        canEdit: permission.canEdit,
        canDelete: permission.canDelete
      });
    });
  }

  // Map file-based menus to permissions
  const menuPermissions = ALL_MENUS.map(menu => {
    // Get user's permission for this menu or use default (no access)
    const permission = permissionsMap.get(menu.id) || {
      canView: false,
      canCreate: false,
      canEdit: false,
      canDelete: false
    };

    // Dashboard always has full permissions and cannot be deselected
    if (menu.name === 'dashboard') {
      permission.canView = true;
      permission.canCreate = true;
      permission.canEdit = true;
      permission.canDelete = true;
    }

    // Return menu with permissions
    return {
      menuId: menu.id,
      name: menu.name,
      displayName: menu.displayName,
      path: menu.path,
      userId,
      tenantId: tenantId || '',
      canView: permission.canView,
      canCreate: permission.canCreate,
      canEdit: permission.canEdit,
      canDelete: permission.canDelete,
      order: menu.order
    };
  });

  // If no permissions provided, return only dashboard
  if (!userPermissions || userPermissions.length === 0) {
    return menuPermissions.filter(menu => menu.name === 'dashboard');
  }

  // Sort menu permissions by order and filter to only include menus the user can view
  return menuPermissions
    .filter(menu => menu.canView)
    .sort((a, b) => a.order - b.order);
}
