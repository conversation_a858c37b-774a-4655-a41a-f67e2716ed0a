import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { paymentMethods } from "@/db/schema";
import { auth } from "~/auth";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Get tenant ID based on user role
    let tenantId = session.user.id;
    if (session.user.role === 'tenant_sale') {
      tenantId = session.user.tenantId || session.user.id;
    }

    // Fetch payment methods for the tenant
    const methods = await db.query.paymentMethods.findMany({
      where: (paymentMethods, { eq }) => eq(paymentMethods.tenantId, tenantId),
      orderBy: (paymentMethods, { asc }) => [asc(paymentMethods.name)],
    });

    return NextResponse.json({
      success: true,
      paymentMethods: methods,
    });
  } catch (error) {
    console.error("Error fetching payment methods:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Only tenant and admin users can create payment methods
    if (session.user.role !== 'tenant' && session.user.role !== 'admin') {
      return NextResponse.json({ message: "Forbidden" }, { status: 403 });
    }

    const data = await request.json();

    // Validate required fields
    if (!data.name || !data.code) {
      return NextResponse.json(
        { message: "Name and code are required" },
        { status: 400 }
      );
    }

    // Get tenant ID
    let tenantId = session.user.id;
    if (session.user.role === 'admin' && data.tenantId) {
      tenantId = data.tenantId;
    }

    // Check if code already exists for this tenant
    const existingMethod = await db.query.paymentMethods.findFirst({
      where: (paymentMethods, { eq, and }) => 
        and(
          eq(paymentMethods.tenantId, tenantId),
          eq(paymentMethods.code, data.code)
        ),
    });

    if (existingMethod) {
      return NextResponse.json(
        { message: "Payment method code already exists" },
        { status: 400 }
      );
    }

    // If this is set as default, unset other defaults
    if (data.isDefault) {
      await db.update(paymentMethods)
        .set({ isDefault: false })
        .where(eq(paymentMethods.tenantId, tenantId));
    }

    // Create the payment method
    const newMethod = await db.insert(paymentMethods).values({
      id: uuidv4(),
      tenantId,
      name: data.name,
      code: data.code,
      description: data.description || null,
      isActive: data.isActive !== undefined ? data.isActive : true,
      isDefault: data.isDefault || false,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // Revalidate relevant pages
    revalidatePath("/dashboard/payment-methods");
    revalidatePath("/dashboard/sales");
    revalidatePath("/dashboard/payments");

    return NextResponse.json(
      { 
        message: "Payment method created successfully",
        paymentMethod: newMethod[0]
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating payment method:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
