import { auth, signOut } from "../../../auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import DynamicSidebar from "@/app/components/DynamicSidebar";
import BranchDisplay from "@/app/components/BranchDisplay";
import ProfileDropdown from "@/app/components/ProfileDropdown";
import ClientProvider from "./client-provider";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  async function handleSignOut() {
    'use server';
    await signOut();
    redirect('/');
  }

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/");
  }

  return (
    <ClientProvider>
      <div className="flex h-screen bg-gray-100 dark:bg-gray-900 overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 bg-white dark:bg-gray-800 shadow-md flex flex-col h-screen">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-center flex-shrink-0">
            <Link href="/dashboard">
              <Image
                src="/logo.webp"
                alt="Hisab Manager Logo"
                width={100}
                height={40}
                className="cursor-pointer"
                priority
              />
            </Link>
          </div>
          <div className="flex-1 overflow-y-auto">
            <DynamicSidebar />
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          {/* Header */}
          <header className="bg-white dark:bg-gray-800 shadow-md p-4 flex flex-wrap justify-between items-center gap-4">
            <div className="flex flex-wrap items-center gap-2">
              <Link
                href="/dashboard/sales/new"
                className="px-3 py-1 bg-gray-800 text-white rounded-md hover:bg-gray-900 text-sm"
              >
                New Invoice
              </Link>
              <Link
                href="/dashboard/products/new"
                className="px-3 py-1 bg-gray-800 text-white rounded-md hover:bg-gray-900 text-sm"
              >
                Add Product
              </Link>
              <Link
                href="/dashboard/customers/new"
                className="px-3 py-1 bg-gray-800 text-white rounded-md hover:bg-gray-900 text-sm"
              >
                Add Customer
              </Link>
              <Link
                href="/dashboard/dealers/purchases/new"
                className="px-3 py-1 bg-gray-800 text-white rounded-md hover:bg-gray-900 text-sm"
              >
                Add Dealer purchase
              </Link>
              <Link
                href="/dashboard/products/categories/new"
                className="px-3 py-1 bg-gray-800 text-white rounded-md hover:bg-gray-900 text-sm"
              >
                Add Category
              </Link>
              <Link
                href="/dashboard/inventory/new"
                className="px-3 py-1 bg-gray-800 text-white rounded-md hover:bg-gray-900 text-sm"
              >
                Add Inventory
              </Link>
              <Link
                href="/dashboard/expenses/new"
                className="px-3 py-1 bg-gray-800 text-white rounded-md hover:bg-gray-900 text-sm"
              >
                Add Expense
              </Link>
              {/* CurrentTime component removed from topbar */}
            </div>
            <div className="flex flex-wrap items-center gap-4">
              {session.user.branchId && (
                <BranchDisplay branchId={session.user.branchId} />
              )}
              <ProfileDropdown user={session.user} onSignOut={handleSignOut} />
            </div>
          </header>

          {/* Page Content */}
          <main className="p-6">
            {children}
          </main>
        </div>
      </div>
    </ClientProvider>
  );
}