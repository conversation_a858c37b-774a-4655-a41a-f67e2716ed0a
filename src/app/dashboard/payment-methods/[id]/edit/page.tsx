import { auth } from "../../../../../../auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { paymentMethods } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import PaymentMethodForm from "../../_components/payment-method-form";

type PageProps = {
  params: {
    id: string;
  };
};

export default async function EditPaymentMethodPage({ params }: PageProps) {
  const session = await auth();

  if (!session?.user) {
    redirect("/");
  }

  // Only tenant and admin users can edit payment methods
  if (session.user.role !== 'tenant' && session.user.role !== 'admin') {
    redirect("/dashboard");
  }

  const { id } = params;

  // Get tenant ID based on user role
  let tenantId = session.user.id;

  // Fetch the payment method
  const method = await db.query.paymentMethods.findFirst({
    where: and(
      eq(paymentMethods.id, id),
      eq(paymentMethods.tenantId, tenantId)
    ),
  });

  if (!method) {
    redirect("/dashboard/payment-methods");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Edit Payment Method</h1>
        <p className="text-muted-foreground">
          Update the details for "{method.name}"
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Method Details</CardTitle>
          <CardDescription>
            Modify the payment method information. Changes will be reflected across all forms.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentMethodForm method={method} />
        </CardContent>
      </Card>
    </div>
  );
}
